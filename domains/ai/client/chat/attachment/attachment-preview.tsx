import type React from 'react';
import { useState } from 'react';
import { useTRPC } from '@bika/api-caller/context';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import { Button } from '@bika/ui/button';
import { isImage } from '@bika/ui/file';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Box } from '@bika/ui/layouts';
import { getAttachmentDisplayPath } from '../ai-chat-message';
import { AttachmentCard } from './attachment-card';
// import { useInputStore } from '../input/input-store';

// Uploading Attachement State, client UI state only
export interface UploadingAttachment {
  id: string;
  name: string;
  size: number;
  base64: string;
  mimeType: string;
}

// AttachmentVO with full url
export type PreviewAttachment = AttachmentVO & { url: string }; // Attachment & { id: string; size?: number };

type AttachmentData =
  | {
      type: 'preview';
      attachment: PreviewAttachment;
    }
  | {
      type: 'uploading';
      attachment: UploadingAttachment;
    };
interface AttachmentPreviewProps {
  data: AttachmentData;

  attachments: AttachmentData[];
  setAttachments: (data: AttachmentData[]) => void;
  onClickDelete?: (data: AttachmentData) => void;
}

export const AttachmentPreview: React.FC<AttachmentPreviewProps> = (props) => {
  const {
    attachments,
    setAttachments,
    data: { type, attachment },
  } = props;
  const trpc = useTRPC();
  // const { attachments = [], setAttachments } = useInputStore();

  const [isDeletingAttachment, setIsDeletingAttachment] = useState(false);
  const deleteAttachment = async (id: string): Promise<void> => {
    setIsDeletingAttachment(true);
    await trpc.attachment.delete.mutate({ id });
    setAttachments(attachments.filter((att) => att.id !== id));
    setIsDeletingAttachment(false);
  };

  const fileSize: number | undefined = 'size' in attachment ? (attachment.size as number) : undefined;
  const imageSrc =
    type === 'uploading' && isImage({ name: attachment.name, type: attachment.mimeType })
      ? attachment.base64
      : getAttachmentDisplayPath({
          name: attachment.name,
          mimeType: attachment.mimeType,
          url: 'url' in attachment ? attachment.url : '',
        });

  const isUploading = type === 'uploading';
  const isDeleting = isDeletingAttachment;

  return (
    <Box
      sx={{
        position: 'relative',
        borderRadius: '4px',
        boxSizing: 'border-box',
        background: 'var(--bg-controls)',
        border: '1px solid var(--border-default)',
        maxWidth: '266px',
      }}
    >
      {isUploading ? (
        <Button
          loading
          variant="plain"
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '32px',
            height: '32px',
            background: 'transparent !important',
          }}
        >
          {''}
        </Button>
      ) : (
        // PreviewAttachment
        <Box
          onClick={() => {
            console.log('Attachment delete clicked:', attachment);
            deleteAttachment(String(attachment.id));
          }}
          sx={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            background: 'var(--bg-controls)',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '16px',
            height: '16px',
            backgroundColor: 'var(--bg-mask)',
            cursor: isDeleting ? 'not-allowed' : 'pointer',
            '&:hover': {
              background: 'var(--bg-controls-hover)',
            },
          }}
        >
          <CloseOutlined color="var(--static)" size={8} />
        </Box>
      )}
      <AttachmentCard
        id={String(attachment.id)}
        name={attachment.name ?? ''}
        contentType={attachment.mimeType ?? ''}
        fileSize={fileSize}
        imageSrc={imageSrc}
      />
    </Box>
  );
};
