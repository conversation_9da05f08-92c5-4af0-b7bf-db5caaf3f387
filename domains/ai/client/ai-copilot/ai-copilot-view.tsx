import assert from 'assert';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { SpaceUICopilot } from '@bika/types/space/bo';
import { useSpaceContext, useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import { IconButton } from '@bika/ui/button';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import HistoryOutlined from '@bika/ui/icons/components/history_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip-components';
import { AIChatView } from '../chat/ai-chat-view';
import { useAIChatCache } from '../chat/hooks/use-ai-chat-cache';

interface Props {
  copilotState: SpaceUICopilot;
}
function AICopilotViewInternal(props: Props) {
  const spaceContext = useSpaceContext();
  const { copilotState } = props;
  assert(copilotState, 'AICopilotView requires copilotState to be set in space context');

  const { t } = useLocale();
  const { useRootNode } = useSpaceContextForce();
  const { findNode } = useRootNode();
  const selector = { type: 'copilot', copilot: copilotState };
  const [cachedChatId, setCacheChatId] = useAIChatCache(selector);

  const router = useSpaceRouter();

  const searchParams = useSearchParams();

  useEffect(() => {
    const paramWizardId = searchParams.get('wizardId');
    if (paramWizardId && copilotState) {
      setCacheChatId(paramWizardId);
    }
  }, [searchParams]);

  useEffect(() => {
    // 从设置中跳转到 Copilot
    const modifyHTMLParam = searchParams.get('ModifyHTML');
    if (modifyHTMLParam !== null) {
      // cacheChatId(Date.now());
      // 清除 URL 参数
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('ModifyHTML');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`);
    }
  }, [searchParams, router]);

  if (!copilotState) {
    return null;
  }

  const initWizardId = cachedChatId || undefined;
  assert(copilotState.type === 'node', 'Copilot must be a node type');
  const node = findNode(copilotState.nodeId);

  // const PageIcon = node ? ResourceIconMap[node.type] : () => null;

  return (
    <Box sx={{ overflow: 'hidden', width: '100%', height: '100%', position: 'relative' }}>
      <Box
        px={2}
        height="48px"
        justifyContent="space-between"
        alignItems="center"
        display="flex"
        borderBottom="1px solid var(--border-default)"
      >
        <Typography textColor={'var(--text-primary)'} level="h6">
          {t.global.copilot.title}
        </Typography>
        <Stack direction="row" gap={1}>
          <Tooltip title={t.global.copilot.new_chat}>
            <IconButton
              onClick={() => {
                setCacheChatId(undefined);
              }}
            >
              <AddOutlined color="var(--text-primary)" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t.global.copilot.history}>
            <IconButton
              onClick={(e) => {
                e.preventDefault();
                spaceContext?.showUIDrawer({ type: 'ai-history', props: { type: 'AI_COPILOT' } });
              }}
            >
              <HistoryOutlined color="var(--text-primary)" />
            </IconButton>
          </Tooltip>
          <IconButton
            onClick={(e) => {
              e.preventDefault();
              spaceContext?.showAICopilot(null);
            }}
          >
            <CloseOutlined color="var(--text-primary)" />
          </IconButton>
        </Stack>
      </Box>
      <Box
        sx={{
          height: 'calc(100% - 48px)',
        }}
      >
        <AIChatView
          displayMode={'COPILOT'}
          initAIIntent={{
            type: 'COPILOT',
            copilot: copilotState,
            // nodeId,
          }}
          selector={selector}
          initWizardId={initWizardId}
          setCacheChatId={setCacheChatId}
          context={[
            {
              type: 'node',
              node: node!,
              // nodeId: copilotState.nodeId,
            },
          ]}
          // context={[
          //   {
          //     label: (
          //       <div className="flex items-center gap-1 px-[16px] py-[8px] text-[--text-disabled] space-x-2 border border-[--border-default] rounded-lg h-[32px] cursor-pointer">
          //         {node?.type && (
          //           <NodeIcon
          //             value={{ kind: 'node-resource', nodeType: node?.type }}
          //             size={16}
          //             color="var(--text-disabled)"
          //           />
          //         )}
          //         {/* <NodeIcon type={node?.type} size={16} color="var(--text-disabled)" /> */}

          //         <div className="text-b3">{node?.name}</div>
          //       </div>
          //     ),
          //     value: copilotState.nodeId,
          //     type: 'NODE',
          //     fixed: true,
          //   },
          // ]}
          allowContextMenu={['ATTACHMENT']}
        />
      </Box>
    </Box>
  );
}

export function AICopilotView() {
  const spaceContext = useSpaceContext();
  const copilotState = spaceContext?.getAICopilot();
  if (!copilotState) {
    return null;
  }

  return <AICopilotViewInternal copilotState={copilotState} />;
}
