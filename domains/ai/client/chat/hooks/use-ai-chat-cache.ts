import { useLocalStorageState } from 'ahooks';
import { useState, useEffect, useCallback } from 'react';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { type TalkBO } from '@bika/types/space/bo';
import { useSpaceContext } from '@bika/types/space/context';
import { getGlobalIndexedDB } from '@bika/types/website/context';
import { PreviewAttachment } from '../attachment/attachment-preview';

export type AIChatCacheSelector = { type: 'agent'; agent: TalkBO } | { type: 'copilot'; copilot: TalkBO };

/**
 * Helper function to create a composite key for IndexedDB storage
 */
function createCompositeKey(storageKey: string, talkId: string): string {
  return `${storageKey}:${talkId}`;
}

/**
 * Extract talkId from TalkBO based on talk type
 */
function getTalkIdFromTalk(selector: AIChatCacheSelector): string {
  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }

  const talkType = talk.type;
  switch (talkType) {
    case 'node':
      return talk.nodeId;
    case 'expert':
      return talk.expertKey;
    case 'unit':
      return talk.unitId;
    default:
      throw new Error(`Unsupported talk type: ${talkType}`);
  }
}

/**
 * Hook to manage AI chat contexts using IndexedDB
 */
export function useAIChatContexts(selector: AIChatCacheSelector): {
  contexts: AIChatContextVO[];
  setContexts: (contexts: AIChatContextVO[]) => void;
  skillsets: SkillsetVO[]; // SkillsetVO[] - using any for now to avoid import issues
  setSkillsets: (skillsets: SkillsetVO[]) => void;
  inputText: string;
  setInputText: (inputText: string) => void;
  isLoading: boolean;
} {
  const spaceContext = useSpaceContext();
  const [contexts, setContextsState] = useState<AIChatContextVO[]>([]);
  const [skillsets, setSkillsetsState] = useState<SkillsetVO[]>([]);
  const [inputText, setInputTextState] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }

  const storageKey = `ai-chat-cache-${spaceContext?.data.id || 'website'}-${selector.type}-${talk.type}`;

  const talkId = getTalkIdFromTalk(selector);

  const compositeKey = createCompositeKey(storageKey, talkId);

  // Load contexts from IndexedDB on mount
  useEffect(() => {
    const loadContexts = async () => {
      try {
        const globalDB = getGlobalIndexedDB();
        await globalDB.init();

        const stored = await globalDB.get('AI_CHAT_CONTEXTS', compositeKey);
        if (stored) {
          setContextsState(stored.contexts || []);
          setSkillsetsState(stored.skillsets || []);
          setInputTextState(stored.inputText || '');
        }
      } catch (error) {
        console.error('Failed to load chat contexts from IndexedDB:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContexts();
  }, [compositeKey]);

  // Save data to IndexedDB when they change
  const saveToIndexedDB = useCallback(
    async (newContexts?: AIChatContextVO[], newSkillsets?: SkillsetVO[], newInputText?: string) => {
      try {
        const globalDB = getGlobalIndexedDB();
        await globalDB.init();

        const data = {
          id: compositeKey,
          storageKey,
          chatId,
          contexts: newContexts !== undefined ? newContexts : contexts,
          skillsets: newSkillsets !== undefined ? newSkillsets : skillsets,
          inputText: newInputText !== undefined ? newInputText : inputText,
          updatedAt: new Date(),
        };

        await globalDB.put('AI_CHAT_CONTEXTS', data);
      } catch (error) {
        console.error('Failed to save chat data to IndexedDB:', error);
      }
    },
    [compositeKey, storageKey, chatId, contexts, skillsets, inputText],
  );

  const setContexts = useCallback(
    async (newContexts: AIChatContextVO[]) => {
      setContextsState(newContexts);
      await saveToIndexedDB(newContexts);
    },
    [saveToIndexedDB],
  );

  const setSkillsets = useCallback(
    async (newSkillsets: SkillsetVO[]) => {
      setSkillsetsState(newSkillsets);
      await saveToIndexedDB(undefined, newSkillsets);
    },
    [saveToIndexedDB],
  );

  const setInputText = useCallback(
    async (newInputText: string) => {
      setInputTextState(newInputText);
      await saveToIndexedDB(undefined, undefined, newInputText);
    },
    [saveToIndexedDB],
  );

  return {
    contexts,
    setContexts,
    skillsets,
    setSkillsets,
    inputText,
    setInputText,
    isLoading,
  };
}

/**
 *
 * Fetch the cached AI chat ID from local storage.
 *
 * @param selector
 * @returns
 */
export function useAIChatCache(selector: AIChatCacheSelector): [string | undefined, (id: string | undefined) => void] {
  const spaceContext = useSpaceContext();
  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }
  const talkType = talk.type;
  // talk对应普通的各种 ai 聊天，copilot对应 talk 里的附带聊天
  // 'ai-chat-cache-<spaceId|standalone>-<talk|copilot>-<node|expert|...>'
  const cacheName = `ai-chat-cache-${spaceContext?.data.id || 'website'}-${selector.type}-${talkType}`;

  const [chatIds, setChatIds] = useLocalStorageState<Record<string, string | undefined>>(cacheName, {
    defaultValue: {},
  });

  const chatId = getChatIdFromTalk(selector);

  const cacheChatId = chatIds?.[chatId];
  const setCacheChatId = (id: string | undefined) => {
    setChatIds(() => ({
      ...chatIds,
      [chatId]: id,
    }));
  };

  return [cacheChatId, setCacheChatId];
}

export interface ChatCacheData {
  data: {
    chatId: string;
    contexts?: AIChatContextVO[];
  };
  api: {
    setAttachments: (attachments: PreviewAttachment[] | ((prev: PreviewAttachment[]) => PreviewAttachment[])) => void;
    setContexts: (contexts: AIChatContextVO[] | ((prev: AIChatContextVO[]) => AIChatContextVO[])) => void;
  };
}

// export function useAIChatDataByChatId(chatId: string): ChatCacheData {
//   return {
//     data: {
//       chatId: chatId ?? '',
//       contexts: [],
//     },
//     api: {
//       setAttachments: () => {},
//       setContexts: () => {},
//     },
//   };
// }

// export function useAIChatData(selector: AIChatCacheSelector): ChatCacheData {
//   const [chatId] = useAIChatCache(selector);
//   const { contexts, setContexts } = useAIChatContexts(selector);

//   return {
//     data: {
//       chatId: chatId ?? '',
//       contexts,
//     },
//     api: {
//       setAttachments: () => {}, // Keep for backward compatibility
//       setContexts,
//     },
//   };
// }
