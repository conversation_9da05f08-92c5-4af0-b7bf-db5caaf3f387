/**
 * IndexedDB 全局数据库管理模块
 *
 * 统一管理数据库存储的方案：
 *
 * 1. 存储名称管理
 * - 使用 STORE_NAMES 常量统一定义所有存储名称
 * - 避免硬编码字符串，减少拼写错误
 *
 * 2. 存储配置管理
 * - 使用 STORE_CONFIGS 配置数组定义存储结构
 * - 包含存储名称、键路径和索引配置
 * - 用于验证配置完整性，防止遗漏
 *
 * 3. 自动化清理和验证
 * - cleanupOldStores 方法会根据配置自动清理所有存储
 * - validateStoreConfigs 方法会检查配置完整性
 * - 添加新存储时的步骤：
 *   1. 在 STORE_NAMES 中添加名称常量
 *   2. 在 STORE_CONFIGS 中添加配置
 *   3. 在 GlobalIndexedDBSchema 中添加类型定义
 *   4. 创建对应的 createStore 方法
 *   5. 在 createAllStores 中调用创建方法
 *
 * 4. 类型安全
 * - 所有存储名称都有类型约束
 * - 配置和实际使用保持一致性
 */

import {
  openDB,
  DBSchema,
  type IDBPDatabase,
  type StoreNames,
  type IndexNames,
  type IndexKey,
  type StoreValue,
} from 'idb';
import type { AIChatContextVO } from '../ai/vo-ai-chat-context';
import type { NodeMenuVO } from '../node/vo-node';
import { SkillsetVO } from '../skill/vo';
import { TalkDetailVO } from '../space/vo-talk';

export type IDataRecently = NodeMenuVO & {
  spaceId: string;
  indexedTime: Date; // indexed time
};

export type IDataSpaceTalk = TalkDetailVO & {
  spaceId: string;
};

export type IDataAIChatContext = {
  id: string; // composite key: ${storageKey}:${chatId}
  storageKey: string;
  talkId: string;
  cacheId: string;
  contexts?: AIChatContextVO[];
  skillsets?: SkillsetVO[];
  inputText?: string;
  updatedAt: Date;
};

// 数据库存储名称常量
export const STORE_NAMES = {
  RECENTLY_NODE_DETAILS: 'RECENTLY_NODE_DETAILS',
  SPACE_TALKS: 'SPACE_TALKS',
  AI_CHAT_CONTEXTS: 'AI_CHAT_CONTEXTS',
} as const;

// 存储配置类型
type StoreConfig = {
  name: keyof typeof STORE_NAMES;
  keyPath: string;
  indexes: Array<{
    name: string;
    keyPath: string | string[];
  }>;
};

// 存储配置对象
const STORE_CONFIGS: StoreConfig[] = [
  {
    name: 'RECENTLY_NODE_DETAILS',
    keyPath: 'id',
    indexes: [
      { name: 'id', keyPath: 'id' },
      { name: 'spaceId', keyPath: 'spaceId' },
      { name: 'indexedTime', keyPath: 'indexedTime' },
    ],
  },
  {
    name: 'SPACE_TALKS',
    keyPath: 'id',
    indexes: [
      { name: 'id', keyPath: 'id' },
      { name: 'spaceId', keyPath: 'spaceId' },
      { name: 'createdAt', keyPath: 'createdAt' },
      { name: 'updatedAt', keyPath: 'updatedAt' },
    ],
  },
  {
    name: 'AI_CHAT_CONTEXTS',
    keyPath: 'id',
    indexes: [
      { name: 'id', keyPath: 'id' },
      { name: 'storageKey', keyPath: 'storageKey' },
      { name: 'chatId', keyPath: 'chatId' },
      { name: 'updatedAt', keyPath: 'updatedAt' },
    ],
  },
];

// Type definitions, refer to idb typescript part: https://www.npmjs.com/package/idb#examples
export interface GlobalIndexedDBSchema extends DBSchema {
  /**
   * Recently accessed NodeDetailVO details
   * @deprecated 改用 Feed
   */
  [STORE_NAMES.RECENTLY_NODE_DETAILS]: {
    key: string; // id
    value: IDataRecently;
    // indexes
    indexes: {
      id: string;
      spaceId: string;
      indexedTime: Date;
    };
  };

  /**
   * Chat Feeds
   */
  [STORE_NAMES.SPACE_TALKS]: {
    key: string; // id
    value: IDataSpaceTalk;
    // indexes
    indexes: {
      id: string;
      spaceId: string;
      createdAt: Date;
      updatedAt: Date;
    };
  };

  /**
   * AI Chat Contexts
   */
  [STORE_NAMES.AI_CHAT_CONTEXTS]: {
    key: string; // composite key: ${storageKey}:${chatId}
    value: IDataAIChatContext;
    // indexes
    indexes: {
      id: string;
      storageKey: string;
      chatId: string;
      updatedAt: Date;
    };
  };
}

const DB_NAME = 'bika.ai-idb';

export class GlobalIndexedDB {
  private db: IDBPDatabase<GlobalIndexedDBSchema> | null = null;

  private isLoading = true;

  private static instance: GlobalIndexedDB | null = null;

  private constructor() {
    // 私有构造函数，防止外部直接实例化
    this.isLoading = true;
  }

  /**
   * 获取单例实例
   */
  static getInstance(): GlobalIndexedDB {
    if (!GlobalIndexedDB.instance) {
      GlobalIndexedDB.instance = new GlobalIndexedDB();
    }
    return GlobalIndexedDB.instance;
  }

  /**
   * 初始化数据库
   */
  async init(): Promise<void> {
    if (this.db) {
      return; // 已经初始化过了
    }

    this.db = await openDB<GlobalIndexedDBSchema>(DB_NAME, 1, {
      upgrade(db, oldVersion, newVersion) {
        if (oldVersion !== newVersion) {
          // 清理旧的对象存储
          GlobalIndexedDB.cleanupOldStores(db);
        }

        // 创建所有需要的对象存储
        GlobalIndexedDB.createAllStores(db);
      },
    });

    this.isLoading = false;
  }

  /**
   * 清理旧的对象存储
   */
  private static cleanupOldStores(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    // 遍历所有配置的存储名称进行清理
    STORE_CONFIGS.forEach((config) => {
      const storeName = STORE_NAMES[config.name];
      if (db.objectStoreNames.contains(storeName)) {
        console.log(`delete idb ${storeName}`);
        db.deleteObjectStore(storeName);
      }
    });
  }

  /**
   * 创建所有对象存储
   */
  private static createAllStores(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    // 验证配置完整性
    GlobalIndexedDB.validateStoreConfigs();

    // 调用专用的创建方法
    GlobalIndexedDB.createRecentlyNodeDetailsStore(db);
    GlobalIndexedDB.createSpaceTalksStore(db);
    GlobalIndexedDB.createAIChatContextsStore(db);
  }

  /**
   * 验证存储配置的完整性
   * 确保所有存储名称都在配置中定义
   */
  private static validateStoreConfigs() {
    const configuredStores = new Set(STORE_CONFIGS.map((config) => config.name));
    const allStoreNames = Object.keys(STORE_NAMES) as Array<keyof typeof STORE_NAMES>;

    allStoreNames.forEach((storeName) => {
      if (!configuredStores.has(storeName)) {
        console.warn(`存储 ${storeName} 没有在配置中定义，可能会导致创建或清理时遗漏`);
      }
    });
  }

  /**
   * 创建最近访问节点详情存储
   */
  private static createRecentlyNodeDetailsStore(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    const recentlyNodeDetailsStore = db.createObjectStore(STORE_NAMES.RECENTLY_NODE_DETAILS, {
      keyPath: 'id',
    });
    recentlyNodeDetailsStore.createIndex('id', 'id');
    recentlyNodeDetailsStore.createIndex('spaceId', 'spaceId');
    recentlyNodeDetailsStore.createIndex('indexedTime', 'indexedTime');
  }

  /**
   * 创建空间对话存储
   */
  private static createSpaceTalksStore(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    const feedsStore = db.createObjectStore(STORE_NAMES.SPACE_TALKS, {
      keyPath: 'id',
    });
    feedsStore.createIndex('id', 'id');
    feedsStore.createIndex('spaceId', 'spaceId');
    feedsStore.createIndex('createdAt', 'createdAt');
    feedsStore.createIndex('updatedAt', 'updatedAt');
  }

  /**
   * 创建AI聊天上下文存储
   */
  private static createAIChatContextsStore(db: IDBPDatabase<GlobalIndexedDBSchema>) {
    const aiChatContextsStore = db.createObjectStore(STORE_NAMES.AI_CHAT_CONTEXTS, {
      keyPath: 'id',
    });
    aiChatContextsStore.createIndex('id', 'id');
    aiChatContextsStore.createIndex('storageKey', 'storageKey');
    aiChatContextsStore.createIndex('chatId', 'chatId');
    aiChatContextsStore.createIndex('updatedAt', 'updatedAt');
  }

  /**
   * 获取数据库实例
   */
  getDB(): IDBPDatabase<GlobalIndexedDBSchema> | null {
    return this.db;
  }

  /**
   * 是否正在加载
   */
  getIsLoading(): boolean {
    return this.isLoading;
  }

  /**
   * 添加数据
   */
  async add<Name extends StoreNames<GlobalIndexedDBSchema>>(
    storeName: Name,
    value: StoreValue<GlobalIndexedDBSchema, Name>,
  ) {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.add(storeName, value);
  }

  /**
   * 更新或添加数据
   */
  async put<Name extends StoreNames<GlobalIndexedDBSchema>>(
    storeName: Name,
    value: StoreValue<GlobalIndexedDBSchema, Name>,
  ) {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.put(storeName, value);
  }

  /**
   * 获取数据
   */
  async get<Name extends StoreNames<GlobalIndexedDBSchema>>(storeName: Name, key: string) {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.get(storeName, key);
  }

  /**
   * 删除数据
   */
  async delete<Name extends StoreNames<GlobalIndexedDBSchema>>(storeName: Name, key: any) {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.delete(storeName, key);
  }

  /**
   * 从索引获取数据
   */
  async getFromIndex<
    Name extends StoreNames<GlobalIndexedDBSchema>,
    IndexName extends IndexNames<GlobalIndexedDBSchema, Name>,
  >(storeName: Name, indexName: IndexName, indexValue: IndexKey<GlobalIndexedDBSchema, Name, IndexName> | IDBKeyRange) {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.getFromIndex(storeName, indexName, indexValue);
  }

  /**
   * 从索引获取所有数据
   */
  async getAllFromIndex<
    Name extends StoreNames<GlobalIndexedDBSchema>,
    IndexName extends IndexNames<GlobalIndexedDBSchema, Name>,
  >(
    storeName: Name,
    indexName: IndexName,
    indexValue: IndexKey<GlobalIndexedDBSchema, Name, IndexName> | IDBKeyRange,
    count?: number,
  ) {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.getAllFromIndex(storeName, indexName, indexValue, count);
  }
}

/**
 * 获取全局 IndexedDB 实例（推荐使用）
 */
export function getGlobalIndexedDB(): GlobalIndexedDB {
  return GlobalIndexedDB.getInstance();
}

/**
 * 初始化全局 IndexedDB（在应用启动时调用）
 */
export async function initGlobalIndexedDB(): Promise<GlobalIndexedDB> {
  const instance = GlobalIndexedDB.getInstance();
  await instance.init();
  return instance;
}
