import { useLocalStorageState } from 'ahooks';
import { useState, useEffect, useCallback } from 'react';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import { SkillsetVO } from '@bika/types/skill/vo';
import { type TalkBO } from '@bika/types/space/bo';
import { useSpaceContext, useSpaceId } from '@bika/types/space/context';
import { getGlobalIndexedDB } from '@bika/types/website/context';
import { PreviewAttachment } from '../attachment/attachment-preview';

export type AIChatCacheSelector = { type: 'agent'; agent: TalkBO } | { type: 'copilot'; copilot: TalkBO };

export interface AIChatData {
  contexts: AIChatContextVO[];
  skillsets: SkillsetVO[];
  inputText: string;
  cacheId: string;
}

/**
 * Helper function to create a composite key for IndexedDB storage
 */
function createCompositeKey(storageKey: string, talkId: string): string {
  return `${storageKey}:${talkId}`;
}

/**
 * Extract talkId from TalkBO based on talk type
 */
function getTalkIdFromTalk(selector: AIChatCacheSelector): string {
  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }

  const talkType = talk.type;
  switch (talkType) {
    case 'node':
      return talk.nodeId;
    case 'expert':
      return talk.expertKey;
    case 'unit':
      return talk.unitId;
    default:
      throw new Error(`Unsupported talk type: ${talkType}`);
  }
}

/**
 * Hook to manage AI chat contexts using IndexedDB
 */
export function useAIChatData(selector: AIChatCacheSelector): {
  data: AIChatData;
  setSkillsets: (skillsets: SkillsetVO[]) => void;
  setContexts: (contexts: AIChatContextVO[]) => void;
  setInputText: (inputText: string) => void;
  setCacheId: (inputText: string) => void;
  isLoading: boolean;
} {
  const spaceId = useSpaceId();
  const [data, setData] = useState<AIChatData>({
    contexts: [],
    skillsets: [],
    inputText: '',
    cacheId: '',
  });
  const [isLoading, setIsLoading] = useState(true);

  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }

  const storageKey = `ai-chat-cache-${spaceId}-${selector.type}-${talk.type}`;

  const talkId = getTalkIdFromTalk(selector);

  const compositeKey = createCompositeKey(storageKey, talkId);

  // Load contexts from IndexedDB on mount
  useEffect(() => {
    const loadContexts = async () => {
      try {
        const globalDB = getGlobalIndexedDB();
        await globalDB.init();

        const stored = await globalDB.get('AI_CHAT_CONTEXTS', compositeKey);
        if (stored) {
          setData({
            contexts: stored.contexts || [],
            skillsets: stored.skillsets || [],
            inputText: stored.inputText || '',
            cacheId: stored.cacheId || compositeKey,
          });
        }
      } catch (error) {
        console.error('Failed to load chat contexts from IndexedDB:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContexts();
  }, [compositeKey]);

  // Save data to IndexedDB when they change
  const saveToIndexedDB = useCallback(
    async (updates: Partial<AIChatData> = {}) => {
      try {
        const globalDB = getGlobalIndexedDB();
        await globalDB.init();

        const newData = { ...data, ...updates };
        const saveData = {
          id: compositeKey,
          storageKey,
          talkId,
          cacheId: compositeKey,
          contexts: newData.contexts,
          skillsets: newData.skillsets,
          inputText: newData.inputText,
          updatedAt: new Date(),
        };

        await globalDB.put('AI_CHAT_CONTEXTS', saveData);
      } catch (error) {
        console.error('Failed to save chat data to IndexedDB:', error);
      }
    },
    [compositeKey, storageKey, data, talkId],
  );

  const setContexts = useCallback(
    async (newContexts: AIChatContextVO[]) => {
      setData((prev) => ({ ...prev, contexts: newContexts }));
      await saveToIndexedDB({ contexts: newContexts });
    },
    [saveToIndexedDB],
  );

  const setSkillsets = useCallback(
    async (newSkillsets: SkillsetVO[]) => {
      setData((prev) => ({ ...prev, skillsets: newSkillsets }));
      await saveToIndexedDB({ skillsets: newSkillsets });
    },
    [saveToIndexedDB],
  );

  const setInputText = useCallback(
    async (newInputText: string) => {
      setData((prev) => ({ ...prev, inputText: newInputText }));
      await saveToIndexedDB({ inputText: newInputText });
    },
    [saveToIndexedDB],
  );

  const setCacheId = useCallback(
    async (newCacheId: string) => {
      setData((prev) => ({ ...prev, cacheId: newCacheId }));
      await saveToIndexedDB({ cacheId: newCacheId });
    },
    [saveToIndexedDB],
  );

  return {
    data,
    setContexts,
    setSkillsets,
    setCacheId,
    setInputText,
    isLoading,
  };
}

/**
 *
 * Fetch the cached AI chat ID from local storage.
 *
 * @param selector
 * @returns
 */
export function useAIChatCache(selector: AIChatCacheSelector): [string | undefined, (id: string | undefined) => void] {
  const spaceContext = useSpaceContext();
  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }
  const talkType = talk.type;
  // talk对应普通的各种 ai 聊天，copilot对应 talk 里的附带聊天
  // 'ai-chat-cache-<spaceId|standalone>-<talk|copilot>-<node|expert|...>'
  const cacheName = `ai-chat-cache-${spaceContext?.data.id || 'website'}-${selector.type}-${talkType}`;

  const [chatIds, setChatIds] = useLocalStorageState<Record<string, string | undefined>>(cacheName, {
    defaultValue: {},
  });

  const talkId = getTalkIdFromTalk(selector);

  const cacheChatId = chatIds?.[talkId];
  const setCacheChatId = (id: string | undefined) => {
    setChatIds(() => ({
      ...chatIds,
      [talkId]: id,
    }));
  };

  const { data, setCacheId: setCacheIdFromData } = useAIChatData(selector);
  const setCacheChatIdFromData = (id: string | undefined) => {
    setCacheIdFromData(id || '');
    setCacheChatId(id || '');
  };
  console.log(`useAIChatCache data ${data.cacheId} ${cacheChatId} `);
  return [cacheChatId, setCacheChatIdFromData];
}

export interface ChatCacheData {
  data: AIChatData & {
    chatId: string; // Keep for backward compatibility
  };
  api: {
    setAttachments: (attachments: PreviewAttachment[] | ((prev: PreviewAttachment[]) => PreviewAttachment[])) => void;
    setContexts: (contexts: AIChatContextVO[] | ((prev: AIChatContextVO[]) => AIChatContextVO[])) => void;
    setSkillsets: (skillsets: SkillsetVO[] | ((prev: SkillsetVO[]) => SkillsetVO[])) => void;
    setInputText: (inputText: string | ((prev: string) => string)) => void;
  };
}

// export function useAIChatDataByChatId(chatId: string): ChatCacheData {
//   return {
//     data: {
//       chatId: chatId ?? '',
//       contexts: [],
//     },
//     api: {
//       setAttachments: () => {},
//       setContexts: () => {},
//     },
//   };
// }

// export function useAIChatData(selector: AIChatCacheSelector): ChatCacheData {
//   const [chatId] = useAIChatCache(selector);
//   const { contexts, setContexts } = useAIChatContexts(selector);

//   return {
//     data: {
//       chatId: chatId ?? '',
//       contexts,
//     },
//     api: {
//       setAttachments: () => {}, // Keep for backward compatibility
//       setContexts,
//     },
//   };
// }
